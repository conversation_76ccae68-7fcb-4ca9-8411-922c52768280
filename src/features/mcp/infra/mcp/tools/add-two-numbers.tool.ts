import { z } from "zod";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Provider } from "@heronjs/common";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { BaseMcpTool, IMcpTool } from "@features/mcp/domain";
import { RequestHandlerExtra } from "@modelcontextprotocol/sdk/shared/protocol";
import { ServerRequest, ServerNotification, CallToolResult } from "@modelcontextprotocol/sdk/types";

export type AddTwoNumbersToolInputArgs = {
  a: z.ZodNumber;
  b: z.ZodNumber;
};

export type AddTwoNumbersToolParsedInputArgs = {
  a: number;
  b: number;
};

export type AddTwoNumbersToolOutputArgs = {
  result: z.ZodNumber;
};

@Provider({ token: MCP_SVC_INJECT_TOKENS.MCP_TOOLS.ADD_TWO_NUMBERS_TOOL })
export class AddTwoNumbersTool
  extends BaseMcpTool<AddTwoNumbersToolInputArgs, AddTwoNumbersToolOutputArgs>
  implements IMcpTool<AddTwoNumbersToolInputArgs, AddTwoNumbersToolOutputArgs>
{
  private readonly logger: ILogger;

  constructor() {
    super({
      name: "add-two-numbers",
      title: "Add Two Numbers",
      description: "Use this tool to add two numbers together.",
      inputSchema: {
        a: z.number().describe("The first number to add"),
        b: z.number().describe("The second number to add"),
      },
    });
    this.logger = new Logger(this.constructor.name);
    this.logger.info("AddTwoNumbersTool initialized successfully.");
  }

  callback = async (
    args: AddTwoNumbersToolParsedInputArgs,
    _extra: RequestHandlerExtra<ServerRequest, ServerNotification>, // unused but required by interface
  ): Promise<CallToolResult> => {
    // Extract or cast or validate the arguments before using them in the callback
    const a = args.a || 0;
    const b = args.b || 0;

    const result: CallToolResult = {
      content: [
        {
          type: "text",
          text: `The result is ${a + b}`,
        },
      ],
    };
    return result;
  };
}
