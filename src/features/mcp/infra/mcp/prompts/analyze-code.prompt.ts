import { z } from "zod";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Provider } from "@heronjs/common";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { IMcpPrompt, BaseMcpPrompt } from "@features/mcp/domain";
import { PromptCallback } from "@modelcontextprotocol/sdk/server/mcp";
import { RequestHandlerExtra } from "@modelcontextprotocol/sdk/shared/protocol";
import {
  ServerRequest,
  GetPromptResult,
  ServerNotification,
} from "@modelcontextprotocol/sdk/types";

export type AnalyzeCodePromptArgs = {
  code: z.ZodString;
};

export type AnalyzeCodePromptParsedArgs = {
  code: string;
};

@Provider({ token: MCP_SVC_INJECT_TOKENS.MCP_PROMPTS.ANALYZE_CODE })
export class AnalyzeCodePrompt
  extends BaseMcpPrompt<AnalyzeCodePromptArgs>
  implements IMcpPrompt<AnalyzeCodePromptArgs>
{
  private readonly logger: ILogger;

  constructor() {
    super({
      name: "analyze-code",
      title: "Analyze Code",
      description: "Use this prompt to analyze code.",
      argsSchema: {
        code: z.string().describe("The code to analyze"),
      },
    });
    this.logger = new Logger(this.constructor.name);
    this.logger.info("AnalyzeCodePrompt initialized successfully.");
  }

  callback: PromptCallback<AnalyzeCodePromptArgs> = async (
    args: AnalyzeCodePromptParsedArgs,
    _extra: RequestHandlerExtra<ServerRequest, ServerNotification>, // unused but required by interface
  ): Promise<GetPromptResult> => {
    // Extract or cast or validate the arguments before using them in the callback
    const code = args.code || "";

    const result: GetPromptResult = {
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: `Please analyze the following code:\n\n${code}`,
          },
        },
      ],
    };
    return result;
  };
}
