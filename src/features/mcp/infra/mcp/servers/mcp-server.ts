import z from "zod";
import { <PERSON><PERSON><PERSON>, Logger, Provider } from "@heronjs/common";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import * as MCP from "@modelcontextprotocol/sdk/server/mcp.js";
import {
  IMcpTool,
  IMcpPrompt,
  IMcpServer,
  IMcpResource,
  McpPromptArgsSchema,
} from "@features/mcp/domain";

@Provider({ token: MCP_SVC_INJECT_TOKENS.INFRA.MCP_SERVER })
export class McpServer implements IMcpServer {
  private readonly logger: ILogger;
  private readonly server: MCP.McpServer;

  constructor() {
    this.logger = new Logger(this.constructor.name);
    this.logger.info("Initializing MCP Server...");
    this.server = new MCP.McpServer({
      name: "MCP Server",
      version: "1.0.0",
      capabilities: {
        resources: {},
        tools: {},
        prompts: {},
      },
    });
    this.logger.info("MCP Server initialized successfully.");
  }

  getServer() {
    return this.server;
  }

  // Register resources
  registerResource(resource: IMcpResource) {
    this.server.registerResource(
      resource.name,
      resource.uriOrTemplate,
      {
        description: resource.config.description,
        mimeType: resource.config.mimeType,
      },
      resource.callback,
    );
  }

  // Register tools
  registerTool<InputArgs extends z.ZodRawShape, OutputArgs extends z.ZodRawShape>(
    tool: IMcpTool<InputArgs, OutputArgs>,
  ) {
    this.server.registerTool(
      tool.name,
      {
        title: tool.title,
        description: tool.description,
        annotations: tool.annotations,
        inputSchema: tool.inputSchema,
        outputSchema: tool.outputSchema,
      },
      tool.callback,
    );
  }

  // Register prompts
  registerPrompt<Args extends McpPromptArgsSchema>(prompt: IMcpPrompt<Args>) {
    this.server.registerPrompt(
      prompt.name,
      {
        title: prompt.title,
        description: prompt.description,
        argsSchema: prompt.argsSchema,
      },
      prompt.callback,
    );
  }
}
