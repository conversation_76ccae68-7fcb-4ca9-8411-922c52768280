import { z } from "zod";
import { IMcpTool } from "@features/mcp/domain/interfaces/mcp-tool";
import { IMcpResource } from "@features/mcp/domain/interfaces/mcp-resource";
import { IMcpPrompt, McpPromptArgsSchema } from "@features/mcp/domain/interfaces/mcp-prompt";

export interface IMcpServer {
  getServer(): {
    connect: (transport: any) => Promise<void>;
  };
  registerTool<InputArgs extends z.ZodRawShape, OutputArgs extends z.ZodRawShape>(
    tool: IMcpTool<InputArgs, OutputArgs>,
  ): void;
  registerPrompt<Args extends McpPromptArgsSchema>(prompt: IMcpPrompt<Args>): void;
  registerResource(resource: IMcpResource): void;
}
