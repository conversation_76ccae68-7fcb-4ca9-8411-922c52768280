import { z } from "zod";
import { Optional } from "@heronjs/common";
import { PromptCallback } from "@modelcontextprotocol/sdk/server/mcp";

export type McpPromptArgsSchema = {
  [k: string]:
    | z.ZodType<string, z.ZodTypeDef, string>
    | z.ZodOptional<z.ZodType<string, z.ZodTypeDef, string>>;
};

export type McpPromptConstructorPayload<Args extends McpPromptArgsSchema> = {
  name: string;
  title?: Optional<string>;
  description?: Optional<string>;
  argsSchema?: Optional<Args>;
};

export interface IMcpPrompt<Args extends McpPromptArgsSchema> {
  name: string;
  title: Optional<string>;
  description: Optional<string>;
  argsSchema: Optional<Args>;
  callback: PromptCallback<Args>;
}

export abstract class BaseMcpPrompt<Args extends McpPromptArgsSchema> implements IMcpPrompt<Args> {
  private readonly _name: string;
  private readonly _title: Optional<string>;
  private readonly _description: Optional<string>;
  private readonly _argsSchema: Optional<Args>;

  constructor({ name, title, description, argsSchema }: McpPromptConstructorPayload<Args>) {
    this._name = name;
    this._title = title;
    this._description = description;
    this._argsSchema = argsSchema;
  }

  get name(): string {
    return this._name;
  }

  get title(): Optional<string> {
    return this._title;
  }

  get description(): Optional<string> {
    return this._description;
  }

  get argsSchema(): Optional<Args> {
    return this._argsSchema;
  }

  abstract callback: PromptCallback<Args>;
}
