import { z } from "zod";
import { Optional } from "@heronjs/common";
import { ToolAnnotations } from "@modelcontextprotocol/sdk/types";
import { ToolCallback } from "@modelcontextprotocol/sdk/server/mcp";

export type McpToolConstructorPayload<
  InputArgs extends z.ZodRawShape | undefined,
  OutputArgs extends z.ZodRawShape | undefined,
> = {
  name: string;
  title?: Optional<string>;
  description?: Optional<string>;
  inputSchema?: InputArgs;
  outputSchema?: OutputArgs;
  annotations?: ToolAnnotations;
};

export interface IMcpTool<
  InputArgs extends z.ZodRawShape | undefined = undefined,
  OutputArgs extends z.ZodRawShape | undefined = undefined,
> {
  name: string;
  title: Optional<string>;
  description: Optional<string>;
  inputSchema?: InputArgs;
  outputSchema?: OutputArgs;
  annotations?: ToolAnnotations;
  callback: Tool<PERSON>allback<InputArgs>;
}

export abstract class BaseMcpTool<
  InputArgs extends z.ZodRawShape | undefined,
  OutputArgs extends z.ZodRawShape | undefined,
> implements IMcpTool<InputArgs, OutputArgs>
{
  private readonly _name: string;
  private readonly _title: Optional<string>;
  private readonly _description: Optional<string>;
  private readonly _inputSchema?: InputArgs;
  private readonly _outputSchema?: OutputArgs;
  private readonly _annotations?: ToolAnnotations;

  constructor({
    name,
    title,
    description,
    inputSchema,
    outputSchema,
    annotations,
  }: McpToolConstructorPayload<InputArgs, OutputArgs>) {
    this._name = name;
    this._title = title;
    this._description = description;
    this._inputSchema = inputSchema;
    this._outputSchema = outputSchema;
    this._annotations = annotations;
  }

  get name(): string {
    return this._name;
  }

  get title(): Optional<string> {
    return this._title;
  }

  get inputSchema(): Optional<InputArgs> {
    return this._inputSchema;
  }

  get outputSchema(): Optional<OutputArgs> {
    return this._outputSchema;
  }

  get description(): Optional<string> {
    return this._description;
  }

  get annotations(): Optional<ToolAnnotations> {
    return this._annotations;
  }

  abstract callback: ToolCallback<InputArgs>;
}
