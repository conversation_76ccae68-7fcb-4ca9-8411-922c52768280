import { z } from "zod";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { ILogger, Inject, Logger, OnComplete, OnStart, Service } from "@heronjs/common";
import {
  IMcpTool,
  IMcpPrompt,
  IMcpServer,
  IMcpResource,
  McpPromptArgsSchema,
} from "@features/mcp/domain";

@Service({ mode: "Initialize" })
export class RegistryService {
  private readonly logger: ILogger;

  constructor(
    @Inject(MCP_SVC_INJECT_TOKENS.INFRA.MCP_SERVER) private readonly mcpServer: IMcpServer,
  ) {
    this.logger = new Logger(RegistryService.name);
  }

  @OnStart()
  onStart() {
    this.logger.info("RegistryService started.");

    // Register tools
    for (const token of Object.values(MCP_SVC_INJECT_TOKENS.MCP_TOOLS)) {
      const tool: IMcpTool<z.Zod<PERSON>aw<PERSON>ha<PERSON>, z.ZodRawShape> = use("Container").resolve(token);
      this.mcpServer.registerTool(tool);
    }

    // Register resources
    for (const token of Object.values(MCP_SVC_INJECT_TOKENS.MCP_RESOURCES)) {
      const resource: IMcpResource = use("Container").resolve(token);
      this.mcpServer.registerResource(resource);
    }

    // Register prompts
    for (const token of Object.values(MCP_SVC_INJECT_TOKENS.MCP_PROMPTS)) {
      const prompt: IMcpPrompt<McpPromptArgsSchema> = use("Container").resolve(token);
      this.mcpServer.registerPrompt(prompt);
    }
  }

  @OnComplete()
  onComplete() {
    this.logger.info("RegistryService completed.");
  }
}
